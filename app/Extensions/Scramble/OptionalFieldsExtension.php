<?php

namespace App\Extensions\Scramble;

use Dedoc\Scramble\Extensions\OperationExtension;
use Dedoc\Scramble\Infer;
use Dedoc\Scramble\Support\Generator\Operation;
use Dedoc\Scramble\Support\Generator\Response;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\Generator\TypeTransformer;
use Dedoc\Scramble\Support\Generator\Types\ObjectType;
use Dedoc\Scramble\Support\RouteInfo;
use Dedoc\Scramble\GeneratorConfig;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;

class OptionalFieldsExtension extends OperationExtension
{
    public function __construct(
        protected Infer $infer,
        protected TypeTransformer $openApiTransformer,
        protected GeneratorConfig $config
    ) {
        parent::__construct($infer, $openApiTransformer, $config);
    }

    public function handle(Operation $operation, RouteInfo $routeInfo): void
    {
        // Получаем ресурс из контроллера
        $resourceClass = $this->getResourceClass($routeInfo);
        
        if (!$resourceClass || !class_exists($resourceClass)) {
            return;
        }

        // Анализируем ресурс и обновляем схему ответа
        $this->processResourceOptionalFields($operation, $resourceClass);
    }

    private function getResourceClass(RouteInfo $routeInfo): ?string
    {
        try {
            $reflectionMethod = $routeInfo->reflectionMethod();
            
            if (!$reflectionMethod) {
                return null;
            }

            // Ищем @response аннотацию в PHPDoc
            $docComment = $reflectionMethod->getDocComment();
            if ($docComment) {
                // Ищем паттерн @response ResourceClass или @response Collection<ResourceClass>
                if (preg_match('/@response\s+([A-Za-z\\\\]+(?:Collection)?(?:<[A-Za-z\\\\]+>)?)/m', $docComment, $matches)) {
                    $resourceAnnotation = $matches[1];
                    
                    // Если это коллекция, извлекаем класс ресурса
                    if (preg_match('/([A-Za-z\\\\]+)Collection<([A-Za-z\\\\]+)>/m', $resourceAnnotation, $collectionMatches)) {
                        return $this->resolveResourceClass($collectionMatches[2]);
                    }
                    
                    // Если это обычный ресурс
                    if (preg_match('/([A-Za-z\\\\]+)(?:Resource|Collection)$/m', $resourceAnnotation, $resourceMatches)) {
                        return $this->resolveResourceClass($resourceAnnotation);
                    }
                }
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function resolveResourceClass(string $className): string
    {
        // Если класс уже содержит полный namespace
        if (str_contains($className, '\\')) {
            return $className;
        }

        // Пытаемся найти класс в стандартных местах
        $possiblePaths = [
            "App\\Http\\Resources\\{$className}",
            "App\\Http\\Resources\\Procurement\\Acceptances\\{$className}",
            "App\\Http\\Resources\\Procurement\\VendorOrders\\{$className}",
            "App\\Http\\Resources\\Sales\\CustomerOrders\\{$className}",
            "App\\Http\\Resources\\Sales\\Shipments\\{$className}",
        ];

        foreach ($possiblePaths as $path) {
            if (class_exists($path)) {
                return $path;
            }
        }

        return $className;
    }

    private function processResourceOptionalFields(Operation $operation, string $resourceClass): void
    {
        try {
            $reflection = new \ReflectionClass($resourceClass);
            
            // Проверяем, что это JsonResource
            if (!$reflection->isSubclassOf(JsonResource::class)) {
                return;
            }

            // Получаем метод toArray
            if (!$reflection->hasMethod('toArray')) {
                return;
            }

            $toArrayMethod = $reflection->getMethod('toArray');
            $docComment = $toArrayMethod->getDocComment();
            
            if (!$docComment) {
                return;
            }

            // Извлекаем поля с @optional аннотациями
            $optionalFields = $this->extractOptionalFields($docComment);
            
            if (empty($optionalFields)) {
                return;
            }

            // Обновляем схему ответа
            $this->updateResponseSchema($operation, $optionalFields);

        } catch (\Exception $e) {
            // Игнорируем ошибки
        }
    }

    private function extractOptionalFields(string $docComment): array
    {
        $optionalFields = [];
        
        // Ищем все @var аннотации с @optional
        $lines = explode("\n", $docComment);
        
        foreach ($lines as $line) {
            // Паттерн: /** @var type $field_name Description @optional */
            if (preg_match('/\*\s*@var\s+[^$]+\$(\w+).*@optional/i', $line, $matches)) {
                $optionalFields[] = $matches[1];
            }
        }

        return $optionalFields;
    }

    private function updateResponseSchema(Operation $operation, array $optionalFields): void
    {
        // Получаем существующие ответы
        $responses = $operation->responses;
        
        foreach ($responses as $statusCode => $response) {
            if ($statusCode === '200' && $response instanceof Response) {
                $content = $response->content;
                
                if (isset($content['application/json'])) {
                    $schema = $content['application/json']->schema;
                    
                    if ($schema instanceof Schema && $schema->type instanceof ObjectType) {
                        $this->markFieldsAsOptional($schema->type, $optionalFields);
                    }
                }
            }
        }
    }

    private function markFieldsAsOptional(ObjectType $objectType, array $optionalFields): void
    {
        // Если это объект с data (коллекция)
        if (isset($objectType->properties['data'])) {
            $dataProperty = $objectType->properties['data'];
            
            // Если data это массив объектов
            if ($dataProperty instanceof \Dedoc\Scramble\Support\Generator\Types\ArrayType && 
                $dataProperty->items instanceof ObjectType) {
                $this->removeRequiredFields($dataProperty->items, $optionalFields);
            }
        } else {
            // Если это обычный объект ресурса
            $this->removeRequiredFields($objectType, $optionalFields);
        }
    }

    private function removeRequiredFields(ObjectType $objectType, array $optionalFields): void
    {
        if (!property_exists($objectType, 'required') || !is_array($objectType->required)) {
            return;
        }

        // Удаляем опциональные поля из списка обязательных
        $objectType->required = array_values(array_diff($objectType->required, $optionalFields));
    }
}
