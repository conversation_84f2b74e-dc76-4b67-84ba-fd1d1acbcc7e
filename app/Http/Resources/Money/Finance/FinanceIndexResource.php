<?php

namespace App\Http\Resources\Money\Finance;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FinanceIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор платежа */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $status_id Идентификатор статуса */
            'status_id' => $this->status_id,
            /** @var string $number Номер платежа */
            'number' => (string) $this->number,
            /** @var string $date_from Дата платежа */
            'date_from' => $this->date_from,
            /** @var bool $held Проведен */
            'held' => (bool) $this->held,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->legal_entity_id,
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string|null $sales_channel_id Идентификатор канала продаж */
            'sales_channel_id' => $this->sales_channel_id,
            /** @var string $sum Сумма */
            'sum' => (string) $this->sum,
            /** @var string $included_vat НДС включен */
            'included_vat' => (string) $this->included_vat,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,
            /** @var string $bounded_sum Связанная сумма */
            'bounded_sum' => (string) $this->bounded_sum,
            /** @var string $not_bounded_sum Несвязанная сумма */
            'not_bounded_sum' => (string) $this->not_bounded_sum,
            /** @var bool $is_imported Импортирован */
            'is_imported' => (bool) $this->is_imported,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $type Тип платежа */
            'type' => (string) $this->type,
        ];
    }
}
