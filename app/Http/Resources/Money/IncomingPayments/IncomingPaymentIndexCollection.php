<?php

namespace App\Http\Resources\Money\IncomingPayments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class IncomingPaymentIndexCollection extends ResourceCollection
{
    public $resource = IncomingPaymentIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
