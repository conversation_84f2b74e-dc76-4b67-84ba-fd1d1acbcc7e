<?php

namespace App\Http\Resources\Money\IncomingPayments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class IncomingPaymentItemIndexCollection extends ResourceCollection
{
    public $resource = IncomingPaymentItemIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
