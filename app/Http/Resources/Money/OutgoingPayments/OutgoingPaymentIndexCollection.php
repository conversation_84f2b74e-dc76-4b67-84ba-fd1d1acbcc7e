<?php

namespace App\Http\Resources\Money\OutgoingPayments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class OutgoingPaymentIndexCollection extends ResourceCollection
{
    public $resource = OutgoingPaymentIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
