<?php

namespace App\Http\Resources\Other\Suggests;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SuggestPartyResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $value Значение подсказки */
            'value' => (string) $this['value'],
            /** @var string $unrestricted_value Неограниченное значение */
            'unrestricted_value' => (string) $this['unrestricted_value'],
            /** @var array{inn: string|null, kpp: string|null, ogrn: string|null, ogrn_date: string|null, hid: string|null, type: string|null, name: array{full_with_opf: string|null, short_with_opf: string|null, latin: string|null, full: string|null, short: string|null}, okpo: string|null, okato: string|null, oktmo: string|null, okogu: string|null, okfs: string|null, okved: string|null, okved_type: string|null, opf: array{type: string|null, code: string|null, full: string|null, short: string|null}, management: array{name: string|null, post: string|null, disqualified: string|null}, branch_type: string|null, branch_count: int|null, source: string|null, qc: string|null, hid: string|null, state: array{status: string|null, code: string|null, actuality_date: string|null, registration_date: string|null, liquidation_date: string|null}, address: array{value: string|null, unrestricted_value: string|null, data: array}} $data Данные организации */
            'data' => $this['data'] ?? (object) [],
        ];
    }
}
