<?php

namespace App\Http\Resources\Procurement\Acceptances;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceItemIndexResource extends JsonResource
{
    /**
     * @property string $id
     * @property string|null $created_at
     * @property string|null $updated_at
     * @property string $acceptance_id
     * @property string $product_id
     * @property int $quantity
     * @property string $price
     * @property string|null $vat_rate_id
     * @property string $discount
     * @property string $total_price
     * @property string|null $country_id
     * @property string|null $gtd_number
     * @property int $recidual
     * @property array{id: string, title: string, code: string, article: string}|null $product
     * @property array{id: string, name: string, code: string}|null $currency
     */
    public function toArray(Request $request): array
    {
        $result = [
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at ?? null,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at ?? null,
            /** @var string $acceptance_id Идентификатор приемки */
            'acceptance_id' => $this->acceptance_id,
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->product_id,
            /** @var int $quantity Количество товара */
            'quantity' => (int) $this->quantity,
            /** @var string $price Цена товара */
            'price' => (string) $this->price,
            /** @var string|null $vat_rate_id Идентификатор ставки НДС @optional */
            'vat_rate_id' => $this->vat_rate_id ?? null,
            /** @var string $discount Размер скидки @optional */
            'discount' => (string) ($this->discount ?? 0),
            /** @var string $total_price Общая стоимость */
            'total_price' => (string) $this->total_price,
            /** @var string|null $country_id Идентификатор страны @optional */
            'country_id' => $this->country_id ?? null,
            /** @var string|null $gtd_number Номер ГТД @optional */
            'gtd_number' => $this->gtd_number ?? null,
            /** @var int $recidual Остаток (вычисляемое поле) @optional */
            'recidual' => (int) ($this->recidual ?? 0),
        ];

        /** @var array{id: string, title: string, code: string, article: string}|null $product Информация о товаре */
        if (isset($this->product)) {
            $result['product'] = [
                'id' => $this->product['id'],
                'title' => $this->product['title'],
                'code' => $this->product['code'],
                'article' => $this->product['article'],
            ];
        }

        if (isset($this->currency)) {
            /** @var array{id: string, name: string, code: string}|null $currency Информация о валюте */
            $result['currency'] = [
                'id' => $this->currency['id'],
                'name' => $this->currency['name'],
                'code' => $this->currency['code'],
            ];
        }

        return $result;
    }
}
