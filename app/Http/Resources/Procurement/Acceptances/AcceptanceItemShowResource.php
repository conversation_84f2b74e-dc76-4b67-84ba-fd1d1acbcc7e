<?php

namespace App\Http\Resources\Procurement\Acceptances;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceItemShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $result = [];

        // Базовые поля - включаем только если они существуют в данных
        if (isset($this->id)) {
            /** @var string $id Уникальный идентификатор записи */
            $result['id'] = $this->id;
        }

        if (isset($this->created_at)) {
            /** @var string $created_at Дата и время создания записи */
            $result['created_at'] = $this->created_at;
        }

        if (isset($this->updated_at)) {
            /** @var string $updated_at Дата и время последнего обновления записи */
            $result['updated_at'] = $this->updated_at;
        }

        if (isset($this->acceptance_id)) {
            /** @var string $acceptance_id Идентификатор приемки */
            $result['acceptance_id'] = $this->acceptance_id;
        }

        if (isset($this->product_id)) {
            /** @var string $product_id Идентификатор товара */
            $result['product_id'] = $this->product_id;
        }

        if (isset($this->quantity)) {
            /** @var int $quantity Количество товара */
            $result['quantity'] = (int) $this->quantity;
        }

        if (isset($this->price)) {
            /** @var string $price Цена товара */
            $result['price'] = (string) $this->price;
        }

        if (isset($this->vat_rate_id)) {
            /** @var string|null $vat_rate_id Идентификатор ставки НДС */
            $result['vat_rate_id'] = $this->vat_rate_id;
        }

        if (isset($this->discount)) {
            /** @var string $discount Размер скидки */
            $result['discount'] = (string) ($this->discount ?? 0);
        }

        if (isset($this->total_price)) {
            /** @var string $total_price Общая стоимость */
            $result['total_price'] = (string) $this->total_price;
        }

        if (isset($this->country_id)) {
            /** @var string|null $country_id Идентификатор страны */
            $result['country_id'] = $this->country_id;
        }

        if (isset($this->gtd_number)) {
            /** @var string|null $gtd_number Номер ГТД */
            $result['gtd_number'] = $this->gtd_number;
        }

        if (isset($this->recidual)) {
            /** @var int $recidual Остаток (вычисляемое поле) */
            $result['recidual'] = (int) ($this->recidual ?? 0);
        }

        return $result;
    }
}
