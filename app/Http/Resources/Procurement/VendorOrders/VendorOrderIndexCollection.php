<?php

namespace App\Http\Resources\Procurement\VendorOrders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class VendorOrderIndexCollection extends ResourceCollection
{
    public $collects = VendorOrderIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => isset($this->additional['meta']) ? [
                'current_page' => $this->additional['meta']['current_page'],
                'per_page' => $this->additional['meta']['per_page'],
                'total' => $this->additional['meta']['total'],
                'last_page' => $this->additional['meta']['last_page']
            ] : []
        ];
    }
}