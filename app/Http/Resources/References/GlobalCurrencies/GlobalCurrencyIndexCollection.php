<?php

namespace App\Http\Resources\References\GlobalCurrencies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class GlobalCurrencyIndexCollection extends ResourceCollection
{
    public $resource = GlobalCurrencyIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
