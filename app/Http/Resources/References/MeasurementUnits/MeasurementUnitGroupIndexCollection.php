<?php

namespace App\Http\Resources\References\MeasurementUnits;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class MeasurementUnitGroupIndexCollection extends ResourceCollection
{
    public $resource = MeasurementUnitGroupIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
