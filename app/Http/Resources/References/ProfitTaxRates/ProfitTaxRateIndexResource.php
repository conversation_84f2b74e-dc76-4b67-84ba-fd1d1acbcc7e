<?php

namespace App\Http\Resources\References\ProfitTaxRates;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProfitTaxRateIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор ставки налога на прибыль */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var int $rate Ставка налога на прибыль в процентах */
            'rate' => (int) $this->rate,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var bool $is_default По умолчанию */
            'is_default' => (bool) $this->is_default,
        ];
    }
}
