<?php

namespace App\Http\Resources\References\SalesChannelTypes;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class SalesChannelTypeIndexCollection extends ResourceCollection
{
    public $resource = SalesChannelTypeIndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
