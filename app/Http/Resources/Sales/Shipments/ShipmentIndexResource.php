<?php

namespace App\Http\Resources\Sales\Shipments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShipmentIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_common Флаг общего доступа */
            'is_common' => (bool) $this->is_common,
            /** @var string $payment_status Статус оплаты (paid|unpaid) */
            'payment_status' => (string) $this->payment_status,
            /** @var string $number Номер документа */
            'number' => (string) $this->number,
            /** @var string $date_from Дата начала действия */
            'date_from' => $this->date_from,
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->status_id,
            /** @var bool $held Флаг удержания */
            'held' => (bool) $this->held,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->legal_entity_id,
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string $warehouse_id Идентификатор склада */
            'warehouse_id' => $this->warehouse_id,
            /** @var string|null $sales_channel_id Идентификатор канала продаж */
            'sales_channel_id' => $this->sales_channel_id,
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->currency_id,
            /** @var string $currency_value Значение валюты */
            'currency_value' => (string) $this->currency_value,
            /** @var string|null $consignee_id Идентификатор получателя */
            'consignee_id' => $this->consignee_id,
            /** @var string|null $transporter_id Идентификатор перевозчика */
            'transporter_id' => $this->transporter_id,
            /** @var string|null $cargo_name Наименование груза */
            'cargo_name' => $this->cargo_name,
            /** @var string|null $shipper_instructions Инструкции для перевозчика */
            'shipper_instructions' => $this->shipper_instructions,
            /** @var string|null $venicle Транспортное средство */
            'venicle' => $this->venicle,
            /** @var string|null $venicle_number Номер транспортного средства */
            'venicle_number' => $this->venicle_number,
            /** @var int|null $total_seats Общее количество мест */
            'total_seats' => $this->total_seats,
            /** @var string|null $goverment_contract_id Номер государственного контракта */
            'goverment_contract_id' => $this->goverment_contract_id,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,
            /** @var bool $price_includes_vat Флаг включения НДС в цену */
            'price_includes_vat' => (bool) $this->price_includes_vat,
            /** @var string $overhead_cost Сверхнормативные расходы */
            'overhead_cost' => (string) $this->overhead_cost,
            /** @var string $total_cost Общая стоимость */
            'total_cost' => (string) $this->total_cost,
            /** @var string $profit Прибыль */
            'profit' => (string) $this->profit,
            /** @var string $total_price Общая сумма */
            'total_price' => (string) $this->total_price,
        ];
    }
}
