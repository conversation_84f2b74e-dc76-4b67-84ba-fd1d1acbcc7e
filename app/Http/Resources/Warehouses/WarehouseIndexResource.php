<?php

namespace App\Http\Resources\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор склада */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $name Название склада */
            'name' => (string) $this->name,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string|null $work_schedule_id Идентификатор рабочего расписания */
            'work_schedule_id' => $this->work_schedule_id,
            /** @var bool $control_free_residuals Контроль свободных остатков */
            'control_free_residuals' => (bool) $this->control_free_residuals,
            /** @var string|null $address_id Идентификатор адреса */
            'address_id' => $this->address_id,
            /** @var string|null $phone_id Идентификатор телефона */
            'phone_id' => $this->phone_id,
            /** @var string|null $responsible_employee_id Идентификатор ответственного сотрудника */
            'responsible_employee_id' => $this->responsible_employee_id,
            /** @var bool $is_default Склад по умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var bool $is_common Общий склад */
            'is_common' => (bool) $this->is_common,
            /** @var string|null $group_id Идентификатор группы складов */
            'group_id' => $this->group_id,
        ];
    }
}
