<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Products\ProductAttributePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Goods\Products\ProductAttributesService\DTO\ProductAttributeDto;

readonly class ProductAttributePolicy implements ProductAttributePolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProductAttributeDto) {
            return;
        }

        $this->authService->validateRelationAccess('products', $dto->productId);

        if ($dto->attributeId) {
            $this->authService->validateRelationAccess('attributes', $dto->attributeId);
        }

        if ($dto->attributeValuesId) {
            $this->authService->validateRelationAccess('attribute_values', $dto->attributeValuesId);
        }
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProductAttributeDto) {
            return;
        }

        $attributeValues = $this->authService->validateRelationAccess('product_attributes', $dto->resourceId);

        if ($attributeValues->attribute_id != $dto->attributeId) {
            $this->authService->validateRelationAccess('attributes', $dto->attributeId);
        }
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('product_attributes', $resourceId);
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('product_attributes', $resourceId);
    }

    public function index(string $resourceId): void
    {
        $this->authService->validateRelationAccess('products', $resourceId);
    }

    public function getByProductId(string $resourceId): void
    {
        $this->authService->validateRelationAccess('products', $resourceId);
    }

}
