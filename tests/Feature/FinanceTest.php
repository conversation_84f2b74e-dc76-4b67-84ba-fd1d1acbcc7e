<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\IncomingPayment;
use App\Models\OutgoingPayment;
use App\Models\Status;
use App\Models\Contractor;
use App\Models\SalesChannel;
use App\Models\LegalEntity;
use App\Models\Group;
use App\Models\ContractorGroup;
use App\Enums\Api\Internal\FinanceTypeEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Document;
use App\Models\IncomingPaymentItem;
use App\Models\OutgoingPaymentItem;
use Illuminate\Foundation\Testing\RefreshDatabase;

class FinanceTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_finances_list(): void
    {
        // Создаем входящие платежи
        IncomingPayment::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем исходящие платежи
        OutgoingPayment::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем платежи в другом кабинете
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);
        OutgoingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'status_id',
                        'number',
                        'date_from',
                        'held',
                        'legal_entity_id',
                        'contractor_id',
                        'sales_channel_id',
                        'sum',
                        'included_vat',
                        'comment',
                        'bounded_sum',
                        'not_bounded_sum',
                        'is_imported',
                        'employee_id',
                        'department_id',
                        'type'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 4 платежа из нашего кабинета
        $response->assertJsonCount(4, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_cannot_access_other_cabinet_finances(): void
    {
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'cabinet_id' => 'not-a-uuid', // Неверный формат UUID
            'filters' => [
                'type' => [
                    'value' => 'invalid_type' // Неверный тип финансов
                ]
            ]
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
                'filters.type.value'
            ]);
    }

    public function test_filter_by_finance_type(): void
    {
        // Создаем входящие платежи
        IncomingPayment::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем исходящие платежи
        OutgoingPayment::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Запрашиваем только входящие платежи
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => [
                    'value' => FinanceTypeEnum::INCOMING_PAYMENTS->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals('incoming_payments', $item['type']);
        }

        // Запрашиваем только исходящие платежи
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => [
                    'value' => FinanceTypeEnum::OUTGOING_PAYMENTS->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(3, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals('outgoing_payments', $item['type']);
        }
    }

    public function test_filter_by_sales_channels_in(): void
    {
        // Создаем каналы продаж
        $salesChannel1 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel2 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными каналами продаж
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel2->id
        ]);

        // Фильтруем по конкретным каналам продаж (IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'value' => [$salesChannel1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($salesChannel1->id, $response->json('data.0.sales_channel_id'));
    }

    public function test_filter_by_sales_channels_not_in(): void
    {
        // Создаем каналы продаж
        $salesChannel1 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel2 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными каналами продаж
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel2->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => null
        ]);

        // Фильтруем, исключая конкретные каналы продаж (NOT_IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'value' => [$salesChannel1->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertNotEquals($salesChannel1->id, $item['sales_channel_id']);
        }
    }

    public function test_filter_by_sales_channels_empty(): void
    {
        // Создаем каналы продаж
        $salesChannel1 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel2 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными каналами продаж
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel2->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => null
        ]);

        // Фильтруем по пустому значению (EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertNull($response->json('data.0.sales_channel_id'));
    }

    public function test_filter_by_sales_channels_not_empty(): void
    {
        // Создаем каналы продаж
        $salesChannel1 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel2 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными каналами продаж
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel2->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => null
        ]);

        // Фильтруем по непустому значению (NOT_EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertNotNull($item['sales_channel_id']);
        }
    }

    public function test_filter_by_contractors_in(): void
    {
        // Создаем контрагентов
        $contractor1 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor2 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными контрагентами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);

        // Фильтруем по конкретным контрагентам (IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'value' => [$contractor1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($contractor1->id, $response->json('data.0.contractor_id'));
    }

    public function test_filter_by_contractors_not_in(): void
    {
        // Создаем контрагентов
        $contractor1 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor2 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными контрагентами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);

        // Фильтруем, исключая конкретных контрагентов (NOT_IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'value' => [$contractor1->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($contractor2->id, $response->json('data.0.contractor_id'));
    }

    public function test_filter_by_contractors_empty(): void
    {
        // Создаем контрагента
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платеж с контрагентом
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor->id
        ]);

        // Создаем платеж без контрагента
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Фильтруем по пустому значению контрагента (EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(0, 'data');
    }

    public function test_filter_by_contractors_not_empty(): void
    {
        // Создаем контрагента
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платеж с контрагентом
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor->id
        ]);

        // Создаем платеж без контрагента
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Фильтруем по непустому значению контрагента (NOT_EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($this->cabinet->id, $response->json('data.0.cabinet_id'));
    }

    public function test_filter_by_statuses_in(): void
    {
        // Создаем статусы
        $status1 = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status2 = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными статусами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status2->id
        ]);

        // Фильтруем по конкретным статусам (IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$status1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($status1->id, $response->json('data.0.status_id'));
    }

    public function test_filter_by_statuses_not_in(): void
    {
        // Создаем статусы
        $status1 = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status2 = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными статусами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status2->id
        ]);

        // Фильтруем, исключая конкретные статусы (NOT_IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$status1->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($status2->id, $response->json('data.0.status_id'));
    }

    public function test_filter_by_statuses_empty(): void
    {
        // Создаем статус
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платеж со статусом
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status->id
        ]);

        // Создаем платеж без статуса
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => null
        ]);

        // Фильтруем по пустому значению статуса (EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertNull($response->json('data.0.status_id'));
    }

    public function test_filter_by_statuses_not_empty(): void
    {
        // Создаем статус
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платеж со статусом
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status->id
        ]);

        // Создаем платеж без статуса
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => null
        ]);

        // Фильтруем по непустому значению статуса (NOT_EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertNotNull($response->json('data.0.status_id'));
        $this->assertEquals($status->id, $response->json('data.0.status_id'));
    }

    public function test_filter_by_employee_owners_in(): void
    {
        // Создаем сотрудников и контрагентов
        $employee1 = Employee::factory()->create();
        $employee2 = Employee::factory()->create();

        $contractor1 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee1->id
        ]);
        $contractor2 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee2->id
        ]);
        $contractor3 = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Создаем платежи
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'contractor_id' => $contractor3->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);

        // Фильтруем по конкретным владельцам контрагентов (IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_owners' => [
                    'value' => [$employee1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($contractor1->id, $response->json('data.0.contractor_id'));
        $this->assertEquals($this->cabinet->id, $response->json('data.0.cabinet_id'));
    }

    public function test_filter_by_employee_owners_not_in(): void
    {
        // Создаем сотрудников и контрагентов
        $employee1 = Employee::factory()->create();
        $employee2 = Employee::factory()->create();

        $contractor1 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee1->id
        ]);
        $contractor2 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee2->id
        ]);

        // Создаем платежи
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);

        // Фильтруем, исключая конкретных владельцев контрагентов (NOT_IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_owners' => [
                    'value' => [$employee1->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($contractor2->id, $response->json('data.0.contractor_id'));
    }

    public function test_filter_by_employee_owners_empty(): void
    {
        // Создаем сотрудника и контрагентов
        $employee = Employee::factory()->create();

        $contractor1 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee->id
        ]);
        $contractor2 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $contractor3 = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Создаем платежи
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'contractor_id' => $contractor3->id
        ]);

        // Фильтруем по пустому значению владельца контрагента (EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_owners' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(0, 'data');
    }

    public function test_filter_by_employee_owners_not_empty(): void
    {
        // Создаем сотрудника и контрагентов
        $employee = Employee::factory()->create();

        $contractor1 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee->id
        ]);
        $contractor2 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $contractor3 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем платежи
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'contractor_id' => $contractor3->id
        ]);

        // Фильтруем по непустому значению владельца контрагента (NOT_EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_owners' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_filter_by_department_owners_in(): void
    {
        // Создаем отделы
        $department1 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department2 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными отделами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department2->id
        ]);

        // Фильтруем по конкретным отделам (IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$department1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($department1->id, $response->json('data.0.department_id'));
    }

    public function test_filter_by_department_owners_not_in(): void
    {
        // Создаем отделы
        $department1 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department2 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платежи с разными отделами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department2->id
        ]);

        // Фильтруем, исключая конкретные отделы (NOT_IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$department1->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($department2->id, $response->json('data.0.department_id'));
    }

    public function test_filter_by_department_owners_empty(): void
    {
        // Создаем отдел
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платеж с отделом
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id
        ]);

        // Создаем платеж без отдела
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Фильтруем по пустому значению отдела (EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(0, 'data');
    }

    public function test_filter_by_department_owners_not_empty(): void
    {
        // Создаем отдел
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем платеж с отделом
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id
        ]);

        // Создаем платеж без отдела
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Фильтруем по непустому значению отдела (NOT_EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');


        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_filter_by_contractor_groups_in(): void
    {
        // Создаем группы и контрагентов
        $group1 = Group::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $group2 = Group::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor1 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor2 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Связываем контрагентов с группами
        ContractorGroup::factory()->create([
            'contractor_id' => $contractor1->id,
            'group_id' => $group1->id
        ]);
        ContractorGroup::factory()->create([
            'contractor_id' => $contractor2->id,
            'group_id' => $group2->id
        ]);

        // Создаем платежи
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Фильтруем по конкретной группе контрагентов (IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_groups' => [
                    'value' => [$group1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($contractor1->id, $response->json('data.0.contractor_id'));
    }

    public function test_filter_by_contractor_groups_not_in(): void
    {
        // Создаем группы и контрагентов
        $group1 = Group::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $group2 = Group::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor1 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor2 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Связываем контрагентов с группами
        ContractorGroup::factory()->create([
            'contractor_id' => $contractor1->id,
            'group_id' => $group1->id
        ]);
        ContractorGroup::factory()->create([
            'contractor_id' => $contractor2->id,
            'group_id' => $group2->id
        ]);

        // Создаем платежи
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);

        // Фильтруем, исключая конкретную группу контрагентов (NOT_IN)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_groups' => [
                    'value' => [$group1->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($contractor2->id, $response->json('data.0.contractor_id'));
    }

    public function test_filter_by_contractor_groups_empty(): void
    {
        // Создаем группу и контрагентов
        $group = Group::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor1 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor2 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Связываем первого контрагента с группой
        ContractorGroup::factory()->create([
            'contractor_id' => $contractor1->id,
            'group_id' => $group->id
        ]);

        // Создаем платежи
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);

        // Фильтруем по пустому значению группы контрагентов (EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_groups' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($contractor2->id, $response->json('data.0.contractor_id'));
    }

    public function test_filter_by_contractor_groups_not_empty(): void
    {
        // Создаем группу и контрагентов
        $group = Group::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor1 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor2 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Связываем первого контрагента с группой
        ContractorGroup::factory()->create([
            'contractor_id' => $contractor1->id,
            'group_id' => $group->id
        ]);

        // Создаем платежи
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);

        // Фильтруем по непустому значению группы контрагентов (NOT_EMPTY)
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_groups' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($contractor1->id, $response->json('data.0.contractor_id'));
    }

    public function test_filter_by_sum(): void
    {
        // Создаем платежи с разными суммами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sum' => 100
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sum' => 200
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sum' => 300
        ]);

        // Фильтруем по диапазону сумм
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sum' => [
                    'from' => 150,
                    'to' => 250
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
    }

    public function test_filter_by_period(): void
    {
        // Создаем платежи с разными датами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'created_at' => '2024-01-01 10:00:00'
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'created_at' => '2024-02-01 10:00:00'
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'created_at' => '2024-03-01 10:00:00'
        ]);

        // Фильтруем по периоду
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'period' => [
                    'from' => '01.02.2024 00:00',
                    'to' => '28.02.2024 23:59'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
    }

    public function test_filter_by_search(): void
    {
        // Создаем платежи с разными номерами и комментариями
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'number' => 'TEST-001',
            'comment' => 'Regular payment'
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'number' => 'INV-002',
            'comment' => 'Test payment'
        ]);

        // Поиск по номеру
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'TEST'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data'); // Находит оба платежа (один по номеру, другой по комментарию)
    }

    public function test_filter_by_is_held(): void
    {
        // Создаем платежи с разными статусами проведения
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => true
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => false
        ]);

        // Фильтруем по проведенным платежам
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_held' => [
                    'value' => true
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
    }

    public function test_filter_by_is_common(): void
    {
        // Создаем платежи с разными флагами общего доступа
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => true
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => false
        ]);

        // Фильтруем по общим платежам
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_common' => [
                    'value' => true
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
    }

    public function test_filter_by_legals_in(): void
    {
        // Создаем юр. лица
        $legal1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $legal2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем платежи с разными юр. лицами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legal1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legal2->id
        ]);

        // Фильтруем по конкретному юр. лицу
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'legals' => [
                    'value' => [$legal1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($legal1->id, $response->json('data.0.legal_entity_id'));
    }

    public function test_filter_by_legals_not_in(): void
    {
        // Создаем юр. лица
        $legal1 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $legal2 = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем платежи с разными юр. лицами
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legal1->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legal2->id
        ]);

        // Фильтруем, исключая конкретное юр. лицо
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'legals' => [
                    'value' => [$legal1->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($legal2->id, $response->json('data.0.legal_entity_id'));
    }

    public function test_filter_by_legals_empty(): void
    {
        // Создаем платеж без юр. лица
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем платеж с юр. лицом
        $legal = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legal->id
        ]);

        // Фильтруем по пустому значению юр. лица
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'legals' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(0, 'data');
    }

    public function test_filter_by_legals_not_empty(): void
    {
        // Создаем платеж без юр. лица
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем платеж с юр. лицом
        $legal = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legal->id
        ]);

        // Фильтруем по непустому значению юр. лица
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'legals' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');
    }

    public function test_filter_by_without_closing_documents(): void
    {
        // Создаем исходящий платеж без закрывающих документов
        OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'without_closing_documents' => true
        ]);

        // Создаем исходящий платеж с закрывающими документами
        OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'without_closing_documents' => false
        ]);

        // Фильтруем по платежам без закрывающих документов
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => [
                    'value' => FinanceTypeEnum::OUTGOING_PAYMENTS->value
                ],
                'without_closing_documents' => [
                    'value' => true
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
    }

    public function test_filter_by_updated_at(): void
    {
        // Создаем платеж с определенной датой обновления
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'updated_at' => '2024-01-15 12:00:00'
        ]);

        // Создаем платеж с более поздней датой обновления
        IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'updated_at' => '2024-01-20 12:00:00'
        ]);

        // Фильтруем по диапазону дат обновления
        $response = $this->getJson('/api/internal/payments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'updated_at' => [
                    'from' => '15.01.2024 00:00',
                    'to' => '15.01.2024 23:59'
                ]
            ]
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $this->assertEquals($payment->id, $response->json('data.0.id'));
    }

    public function test_bulk_delete_payments(): void
    {
        // Создаем платежи для удаления
        $payment1 = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $payment2 = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем документы для платежей
        Document::factory()->create([
            'documentable_id' => $payment1->id,
            'documentable_type' => 'incoming_payments',
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1002,
            'rgt' => 1003
        ]);
        Document::factory()->create([
            'documentable_id' => $payment2->id,
            'documentable_type' => 'outgoing_payments',
            'cabinet_id' => $this->cabinet->id,
            'lft' => 1002,
            'rgt' => 1003
        ]);

        $response = $this->deleteJson('/api/internal/payments/bulk-delete', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$payment1->id, $payment2->id]
        ]);

        $response->assertStatus(200);

        // Проверяем что платежи удалены
        $this->assertDatabaseMissing('incoming_payments', ['id' => $payment1->id]);
        $this->assertDatabaseMissing('outgoing_payments', ['id' => $payment2->id]);

        // Проверяем что документы тоже удалены
        $this->assertDatabaseMissing('documents', [
            'documentable_id' => $payment1->id,
            'documentable_type' => 'incoming_payments'
        ]);
        $this->assertDatabaseMissing('documents', [
            'documentable_id' => $payment2->id,
            'documentable_type' => 'outgoing_payments'
        ]);
    }

    public function test_bulk_held_payments(): void
    {
        // Создаем неподтвержденные платежи
        $payment1 = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => false
        ]);
        $payment2 = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => false
        ]);

        $response = $this->patchJson('/api/internal/payments/bulk-held', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$payment1->id, $payment2->id]
        ]);

        $response->assertStatus(200);

        // Проверяем что платежи подтверждены
        $this->assertDatabaseHas('incoming_payments', [
            'id' => $payment1->id,
            'held' => true
        ]);
        $this->assertDatabaseHas('outgoing_payments', [
            'id' => $payment2->id,
            'held' => true
        ]);
    }

    public function test_bulk_unheld_payments(): void
    {
        // Создаем подтвержденные платежи
        $payment1 = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => true
        ]);
        $payment2 = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => true
        ]);

        $response = $this->patchJson('/api/internal/payments/bulk-unheld', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$payment1->id, $payment2->id]
        ]);

        $response->assertStatus(200);

        // Проверяем что платежи сняты с подтверждения
        $this->assertDatabaseHas('incoming_payments', [
            'id' => $payment1->id,
            'held' => false
        ]);
        $this->assertDatabaseHas('outgoing_payments', [
            'id' => $payment2->id,
            'held' => false
        ]);
    }

    public function test_bulk_copy_payments(): void
    {
        // Создаем платежи для копирования
        $payment1 = IncomingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'comment' => 'TEST-COPY-001',
            'sum' => 1000,
            'held' => true
        ]);
        $payment2 = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'comment' => 'TEST-COPY-002',
            'held' => true
        ]);

        // Создаем items для платежей
        IncomingPaymentItem::factory()->create([
            'incoming_payment_id' => $payment1->id,
            'paid_in' => 1000
        ]);
        OutgoingPaymentItem::factory()->create([
            'outgoing_payment_id' => $payment2->id,
            'paid_in' => 2000
        ]);

        $response = $this->postJson('/api/internal/payments/bulk-copy', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$payment1->id, $payment2->id]
        ]);

        $response->assertStatus(200);

        // Проверяем что созданы новые документы
        $incomingPayments = IncomingPayment::query()
            ->where([
                'cabinet_id' => $this->cabinet->id,
                'comment' => 'TEST-COPY-001'
            ])->get();

        $outgoingPayments = OutgoingPayment::query()
            ->where([
                'cabinet_id' => $this->cabinet->id,
                'comment' => 'TEST-COPY-002'
            ])->get();

        $this->assertEquals(2, $incomingPayments->count());
        $this->assertEquals(2, $outgoingPayments->count());

        foreach ($incomingPayments as $payment) {
            $this->assertTrue(
                IncomingPaymentItem::query()
                    ->where('incoming_payment_id', $payment->id)
                    ->exists()
            );
        }
        foreach ($outgoingPayments as $payment) {
            $this->assertTrue(
                OutgoingPaymentItem::query()
                    ->where('outgoing_payment_id', $payment->id)
                    ->exists()
            );
        }

    }

    public function test_cannot_bulk_delete_other_cabinet_payments(): void
    {
        // Создаем платежи в другом кабинете
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->deleteJson('/api/internal/payments/bulk-delete', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$payment->id]
        ]);

        $response->assertStatus(403);

        // Проверяем что платеж не удален
        $this->assertDatabaseHas('incoming_payments', ['id' => $payment->id]);
    }

    public function test_cannot_bulk_held_other_cabinet_payments(): void
    {
        // Создаем платеж в другом кабинете
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'held' => false
        ]);

        $response = $this->patchJson('/api/internal/payments/bulk-held', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$payment->id]
        ]);

        $response->assertStatus(403);

        // Проверяем что платеж не подтвержден
        $this->assertDatabaseHas('incoming_payments', [
            'id' => $payment->id,
            'held' => false
        ]);
    }

    public function test_cannot_bulk_unheld_other_cabinet_payments(): void
    {
        // Создаем платеж в другом кабинете
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'held' => true
        ]);

        $response = $this->patchJson('/api/internal/payments/bulk-unheld', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$payment->id]
        ]);

        $response->assertStatus(403);

        // Проверяем что платеж остался подтвержденным
        $this->assertDatabaseHas('incoming_payments', [
            'id' => $payment->id,
            'held' => true
        ]);
    }

    public function test_cannot_bulk_copy_other_cabinet_payments(): void
    {
        // Создаем платеж в другом кабинете
        $payment = IncomingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->postJson('/api/internal/payments/bulk-copy', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$payment->id]
        ]);

        $response->assertStatus(403);

        // Проверяем что новый платеж не создан
        $this->assertDatabaseCount('incoming_payments', 1);
    }

    public function test_bulk_operations_validation_errors(): void
    {
        $invalidData = [
            'cabinet_id' => 'not-a-uuid',
            'ids' => 'not-an-array'
        ];

        // Проверяем все bulk операции
        $this->deleteJson('/api/internal/payments/bulk-delete', $invalidData)
            ->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id', 'ids']);

        $this->patchJson('/api/internal/payments/bulk-held', $invalidData)
            ->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id', 'ids']);

        $this->patchJson('/api/internal/payments/bulk-unheld', $invalidData)
            ->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id', 'ids']);

        $this->postJson('/api/internal/payments/bulk-copy', $invalidData)
            ->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id', 'ids']);
    }
}
